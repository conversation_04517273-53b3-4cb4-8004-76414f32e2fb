version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/tele-store
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/public/uploads:/app/public/uploads
    networks:
      - app_network
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3000
    depends_on:
      - backend
    networks:
      - app_network
    restart: unless-stopped

  mongodb:
    image: mongo:7.0
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_DATABASE=tele-store
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init:/docker-entrypoint-initdb.d
    networks:
      - app_network
    restart: unless-stopped

  redis:
    image: redis:7.2-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - app_network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  app_network:
    driver: bridge
