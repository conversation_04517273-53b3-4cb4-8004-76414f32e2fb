# TeleStore OAuth Setup Guide

## 🚀 Tổng quan

TeleStore hiện đã hỗ trợ đăng nhập qua:
- **Google OAuth** - Đ<PERSON><PERSON> nhập bằng tài khoản Google
- **Telegram Login** - Đăng nhập bằng tài khoản Telegram
- **Traditional Login** - Đ<PERSON>ng nhập bằng username/password

## 📋 C<PERSON>u hình OAuth

### 1. Bật/Tắt OAuth

Trong file `backend/config/default.json`:

```json
{
  "oauth": {
    "enabled": true,  // Bật/tắt toàn bộ OAuth
    "google": {
      "enabled": true,  // Bật/tắt Google OAuth
      "clientId": "your_google_client_id",
      "clientSecret": "your_google_client_secret",
      "callbackURL": "http://localhost:3000/auth/google/callback"
    },
    "telegram": {
      "enabled": true,  // Bật/tắt Telegram OAuth
      "botToken": "your_telegram_bot_token",
      "callbackURL": "http://localhost:3000/auth/telegram/callback"
    }
  }
}
```

### 2. Setup Google OAuth

#### Bước 1: Tạo Google Cloud Project
1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Enable Google+ API hoặc Google People API

#### Bước 2: Tạo OAuth Credentials
1. Vào **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Chọn **Web application**
4. Thêm **Authorized redirect URIs**:
   - `http://localhost:3000/auth/google/callback` (development)
   - `https://yourdomain.com/auth/google/callback` (production)

#### Bước 3: Cập nhật Config
```json
{
  "oauth": {
    "enabled": true,
    "google": {
      "enabled": true,
      "clientId": "123456789-abcdef.apps.googleusercontent.com",
      "clientSecret": "GOCSPX-your_client_secret",
      "callbackURL": "http://localhost:3000/auth/google/callback"
    }
  }
}
```

### 3. Setup Telegram Login

#### Bước 1: Tạo Telegram Bot
1. Chat với [@BotFather](https://t.me/botfather)
2. Gửi `/newbot` và làm theo hướng dẫn
3. Lưu Bot Token

#### Bước 2: Setup Domain cho Login Widget
1. Chat với [@BotFather](https://t.me/botfather)
2. Gửi `/setdomain`
3. Chọn bot của bạn
4. Nhập domain: `localhost:3000` (development) hoặc `yourdomain.com` (production)

#### Bước 3: Cập nhật Config
```json
{
  "oauth": {
    "enabled": true,
    "telegram": {
      "enabled": true,
      "botToken": "123456789:ABCdefGHIjklMNOpqrsTUVwxyz",
      "callbackURL": "http://localhost:3000/auth/telegram/callback"
    }
  }
}
```

## 🔧 Cài đặt và Chạy

### 1. Cài đặt Dependencies
```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
```

### 2. Cấu hình Database
Đảm bảo MongoDB và Redis đang chạy:
```bash
# MongoDB
mongod

# Redis
redis-server
```

### 3. Chạy Application
```bash
# Backend (Terminal 1)
cd backend
npm start

# Frontend (Terminal 2)
cd frontend
npm start
```

### 4. Test OAuth System
```bash
# Chạy script test
./scripts/test-oauth.sh
```

## 🌐 URLs và Endpoints

### Frontend URLs
- **Login Page**: `http://localhost:3001/login`
- **Main App**: `http://localhost:3001/`
- **Login Success**: `http://localhost:3001/login-success`

### Backend OAuth Endpoints
- **Google OAuth**: `http://localhost:3000/auth/google`
- **Google Callback**: `http://localhost:3000/auth/google/callback`
- **Telegram OAuth**: `http://localhost:3000/auth/telegram`
- **Telegram Callback**: `http://localhost:3000/auth/telegram/callback`
- **Telegram Verify**: `POST http://localhost:3000/api/v1.0/auth/telegram/verify`
- **OAuth Config**: `GET http://localhost:3000/api/v1.0/auth/oauth/config`

### Traditional Auth Endpoints
- **Login**: `POST http://localhost:3000/api/v1.0/auth/login`
- **Register**: `POST http://localhost:3000/api/v1.0/auth/register`

## 🔒 Bảo mật

### User Data Management
- Mỗi user chỉ có thể truy cập files/folders của mình
- OAuth users được tự động tạo account khi đăng nhập lần đầu
- Traditional users cần đăng ký trước khi đăng nhập

### Token Management
- JWT tokens được lưu trong Redis
- Session timeout: 24 giờ (có thể cấu hình)
- Automatic token cleanup khi logout

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **"OAuth is not enabled"**
   - Kiểm tra `oauth.enabled = true` trong config

2. **"Google authentication failed"**
   - Kiểm tra Client ID/Secret
   - Kiểm tra Redirect URI trong Google Console

3. **"Telegram authentication failed"**
   - Kiểm tra Bot Token
   - Kiểm tra domain đã được set trong BotFather

4. **"Token not provided"**
   - User chưa đăng nhập
   - Token đã hết hạn

### Debug Mode
Để debug OAuth flow, check console logs trong:
- Backend: `backend/logs/`
- Frontend: Browser Developer Tools

## 📝 Development Notes

### Database Schema
- **User Model**: Đã được cập nhật với OAuth fields
- **File/Folder Models**: Có `ownerId` để link với user
- **Indexes**: Đã tối ưu cho queries theo user

### Frontend Components
- **LoginPage**: Giao diện đăng nhập với OAuth buttons
- **LoginSuccess**: Xử lý OAuth callback
- **Header**: Hiển thị user info và logout

### Security Features
- CSRF protection với session
- Input validation
- SQL injection prevention
- XSS protection
