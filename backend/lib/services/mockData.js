const { v4: uuidv4 } = require('uuid');

// Mock data for testing without database
let mockFolders = [
  {
    _id: uuidv4(),
    folderName: 'Documents',
    parentId: null,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    isDeleted: false
  },
  {
    _id: uuidv4(),
    folderName: 'Images',
    parentId: null,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    isDeleted: false
  },
  {
    _id: uuidv4(),
    folderName: 'Videos',
    parentId: null,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01'),
    isDeleted: false
  }
];

let mockFiles = [
  {
    _id: uuidv4(),
    fileName: 'presentation.pdf',
    originalName: 'presentation.pdf',
    mimeType: 'application/pdf',
    size: 2048576,
    parentId: null,
    telegramFileId: 'mock_file_1',
    uploadDate: new Date('2024-01-10'),
    isDeleted: false
  },
  {
    _id: uuidv4(),
    fileName: 'avatar.jpg',
    originalName: 'avatar.jpg',
    mimeType: 'image/jpeg',
    size: 512000,
    parentId: null,
    telegramFileId: 'mock_file_2',
    uploadDate: new Date('2024-01-12'),
    isDeleted: false
  },
  {
    _id: uuidv4(),
    fileName: 'config.json',
    originalName: 'config.json',
    mimeType: 'application/json',
    size: 1024,
    parentId: null,
    telegramFileId: 'mock_file_3',
    uploadDate: new Date('2024-01-14'),
    isDeleted: false
  },
  {
    _id: uuidv4(),
    fileName: 'script.js',
    originalName: 'script.js',
    mimeType: 'application/javascript',
    size: 4096,
    parentId: null,
    telegramFileId: 'mock_file_4',
    uploadDate: new Date('2024-01-16'),
    isDeleted: false
  }
];

const mockDataService = {
  // Get folder content
  getFolderContent: async (folderId = null) => {
    const folders = mockFolders.filter(f => f.parentId === folderId && !f.isDeleted);
    const files = mockFiles.filter(f => f.parentId === folderId && !f.isDeleted);
    
    return {
      folders: folders.map(f => ({
        id: f._id,
        name: f.folderName,
        type: 'folder',
        createdAt: f.createdAt,
        updatedAt: f.updatedAt
      })),
      files: files.map(f => ({
        id: f._id,
        name: f.fileName,
        type: 'file',
        size: f.size,
        mimeType: f.mimeType,
        uploadDate: f.uploadDate,
        telegramFileId: f.telegramFileId
      }))
    };
  },

  // Create folder
  createFolder: async (folderName, parentId = null) => {
    const newFolder = {
      _id: uuidv4(),
      folderName,
      parentId,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false
    };
    
    mockFolders.push(newFolder);
    
    return {
      id: newFolder._id,
      name: newFolder.folderName,
      type: 'folder',
      createdAt: newFolder.createdAt,
      updatedAt: newFolder.updatedAt
    };
  },

  // Rename item
  renameItem: async (itemId, newName) => {
    // Find in folders
    const folderIndex = mockFolders.findIndex(f => f._id === itemId);
    if (folderIndex !== -1) {
      mockFolders[folderIndex].folderName = newName;
      mockFolders[folderIndex].updatedAt = new Date();
      return { success: true };
    }

    // Find in files
    const fileIndex = mockFiles.findIndex(f => f._id === itemId);
    if (fileIndex !== -1) {
      mockFiles[fileIndex].fileName = newName;
      mockFiles[fileIndex].originalName = newName;
      mockFiles[fileIndex].updatedAt = new Date();
      return { success: true };
    }

    throw new Error('Item not found');
  },

  // Delete item
  deleteItem: async (itemId) => {
    // Find in folders
    const folderIndex = mockFolders.findIndex(f => f._id === itemId);
    if (folderIndex !== -1) {
      mockFolders[folderIndex].isDeleted = true;
      return { success: true };
    }

    // Find in files
    const fileIndex = mockFiles.findIndex(f => f._id === itemId);
    if (fileIndex !== -1) {
      mockFiles[fileIndex].isDeleted = true;
      return { success: true };
    }

    throw new Error('Item not found');
  },

  // Search
  search: async (query) => {
    const searchTerm = query.toLowerCase();
    
    const folders = mockFolders
      .filter(f => !f.isDeleted && f.folderName.toLowerCase().includes(searchTerm))
      .map(f => ({
        id: f._id,
        name: f.folderName,
        type: 'folder',
        createdAt: f.createdAt,
        updatedAt: f.updatedAt
      }));

    const files = mockFiles
      .filter(f => !f.isDeleted && f.fileName.toLowerCase().includes(searchTerm))
      .map(f => ({
        id: f._id,
        name: f.fileName,
        type: 'file',
        size: f.size,
        mimeType: f.mimeType,
        uploadDate: f.uploadDate,
        telegramFileId: f.telegramFileId
      }));

    return { folders, files };
  },

  // Upload file (mock)
  uploadFile: async (fileData) => {
    const newFile = {
      _id: uuidv4(),
      fileName: fileData.originalname,
      originalName: fileData.originalname,
      mimeType: fileData.mimetype,
      size: fileData.size,
      parentId: fileData.parentId || null,
      telegramFileId: `mock_${uuidv4()}`,
      uploadDate: new Date(),
      isDeleted: false
    };
    
    mockFiles.push(newFile);
    
    return {
      id: newFile._id,
      name: newFile.fileName,
      type: 'file',
      size: newFile.size,
      mimeType: newFile.mimeType,
      uploadDate: newFile.uploadDate,
      telegramFileId: newFile.telegramFileId
    };
  }
};

module.exports = mockDataService;
