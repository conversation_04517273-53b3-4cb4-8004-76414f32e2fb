const winston = require('winston')
const config = require('config')
const _ = require('lodash')
const fs = require('fs')

function setUp(dir) {
  fs.existsSync(dir) || fs.mkdirSync(dir)

  const logLevel = _.get(config, 'logLevel', 'info')

  const logger = winston.createLogger({
    levels: {
      info: 1,
      error: 0
    },
    transports: [
      new winston.transports.Console({
        level: 'info',
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      }),
      new winston.transports.File({
        level: 'error',
        filename: dir + '/system.log',
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      })
    ]
  })

  const obj = {
    logInfo: () => {},
    logError: (...args) => {
      logger.error(args.join(' '))
    }
  }

  if(logLevel === 'info') {
    obj.logInfo = (...args) => {
      logger.info(args.join(' '))
    }
  }

  return obj
}

module.exports = setUp
