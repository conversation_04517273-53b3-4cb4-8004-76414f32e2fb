const mongoConnections = require("../connections/mongo")
const mongoose = require('mongoose')
const Schema = mongoose.Schema
const UserSchema = new mongoose.Schema(
 {
  username: {
   type: String,
   required: true,
   unique: true
  },
  password: {
   type: String,
   required: false // Not required for OAuth users
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  name: {
   type: String,
   required: true
  },
  phone: {
   type: String
  },
  avatar: {
    type: String
  },
  role: {
    type: String,
    default: 'user',
    enum: ['user', 'admin']
  },
  status: {
    type: Number,
    default: 1 // 1: active, 0: inactive
  },
  // OAuth fields
  provider: {
    type: String,
    enum: ['local', 'google', 'telegram'],
    default: 'local'
  },
  googleId: {
    type: String,
    sparse: true,
    unique: true
  },
  telegramId: {
    type: String,
    sparse: true,
    unique: true
  },
  telegramUsername: {
    type: String
  },
  profilePicture: {
    type: String
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String
  },
  emailVerificationExpires: {
    type: Date
  },
  passwordResetToken: {
    type: String
  },
  passwordResetExpires: {
    type: Date
  },
  lastLoginAt: {
    type: Date
  },
  // Storage tracking (using String to handle large numbers safely)
  storageUsed: {
    type: String,
    default: '0' // in bytes as string
  },
  storageQuota: {
    type: String,
    default: '1099511627776000' // 1000TB in bytes as string (1000 * 1024^4)
  },
  // File and folder counts for statistics
  fileCount: {
    type: Number,
    default: 0
  },
  folderCount: {
    type: Number,
    default: 0
  },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("User", UserSchema)
