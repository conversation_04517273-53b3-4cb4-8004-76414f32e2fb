const mongoConnections = require("../connections/mongo")
const mongoose = require('mongoose')
const Schema = mongoose.Schema

const FileSchema = new mongoose.Schema(
  {
    telegramFileId: {
      type: String,
      required: true,
      index: true
    },
    originalFileName: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    uploadDate: {
      type: Date,
      default: Date.now
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: 'Folder',
      default: null,
      index: true
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true
    },
    deletedAt: {
      type: Date,
      default: null
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null,
      index: true
    },
    isProcessingAI: {
      type: Boolean,
      default: false
    },
    aiMetadata: {
      type: Schema.Types.Mixed,
      default: {}
    },
    // Full Telegram API response data
    telegramMetadata: {
      type: Schema.Types.Mixed,
      default: {}
    },
    // Additional file metadata
    fileMetadata: {
      width: { type: Number }, // for images/videos
      height: { type: Number }, // for images/videos
      duration: { type: Number }, // for videos/audio
      thumbnail: { type: String }, // thumbnail file_id if available
      fileUniqueId: { type: String }, // Telegram's unique file identifier
      fileName: { type: String }, // filename from Telegram (may differ from originalFileName)
      performer: { type: String }, // for audio files
      title: { type: String }, // for audio files
      type: Schema.Types.Mixed // additional type-specific metadata
    }
  },
  {
    id: false,
    versionKey: false,
    timestamps: false
  }
)

// Indexes for better query performance
FileSchema.index({ parentId: 1, isDeleted: 1 })
FileSchema.index({ originalFileName: 1, isDeleted: 1 })
FileSchema.index({ uploadDate: -1 })
FileSchema.index({ ownerId: 1, isDeleted: 1 })

module.exports = mongoConnections("master").model("File", FileSchema)
