const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const TelegramStrategy = require('passport-telegram').Strategy;
const config = require('config');
const UserModel = require('../models/user');

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user._id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await UserModel.findById(id).select('-password').lean();
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth Strategy
if (config.get('oauth.enabled') && config.get('oauth.google.enabled')) {
  passport.use(new GoogleStrategy({
    clientID: config.get('oauth.google.clientId'),
    clientSecret: config.get('oauth.google.clientSecret'),
    callbackURL: config.get('oauth.google.callbackURL')
  },
  async (accessToken, refreshToken, profile, done) => {
    try {
      // Check if user already exists with this Google ID
      let user = await UserModel.findOne({ googleId: profile.id });
      
      if (user) {
        // Update last login
        user.lastLoginAt = new Date();
        user.updatedAt = Date.now();
        await UserModel.updateOne({ _id: user._id }, { 
          lastLoginAt: user.lastLoginAt,
          updatedAt: user.updatedAt 
        });
        return done(null, user);
      }

      // Check if user exists with same email
      user = await UserModel.findOne({ email: profile.emails[0].value });
      
      if (user) {
        // Link Google account to existing user
        user.googleId = profile.id;
        user.provider = 'google';
        user.profilePicture = profile.photos[0]?.value;
        user.isEmailVerified = true;
        user.lastLoginAt = new Date();
        user.updatedAt = Date.now();
        
        await UserModel.updateOne({ _id: user._id }, {
          googleId: user.googleId,
          provider: user.provider,
          profilePicture: user.profilePicture,
          isEmailVerified: user.isEmailVerified,
          lastLoginAt: user.lastLoginAt,
          updatedAt: user.updatedAt
        });
        
        return done(null, user);
      }

      // Create new user
      const newUser = new UserModel({
        googleId: profile.id,
        username: profile.emails[0].value.split('@')[0] + '_' + Date.now(),
        email: profile.emails[0].value,
        name: profile.displayName,
        provider: 'google',
        profilePicture: profile.photos[0]?.value,
        avatar: profile.photos[0]?.value,
        isEmailVerified: true,
        status: 1,
        lastLoginAt: new Date(),
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('Google OAuth error:', error);
      done(error, null);
    }
  }));
}

// Telegram OAuth Strategy
if (config.get('oauth.enabled') && config.get('oauth.telegram.enabled')) {
  passport.use(new TelegramStrategy({
    botToken: config.get('oauth.telegram.botToken')
  },
  async (profile, done) => {
    try {
      // Check if user already exists with this Telegram ID
      let user = await UserModel.findOne({ telegramId: profile.id.toString() });
      
      if (user) {
        // Update last login
        user.lastLoginAt = new Date();
        user.updatedAt = Date.now();
        await UserModel.updateOne({ _id: user._id }, { 
          lastLoginAt: user.lastLoginAt,
          updatedAt: user.updatedAt 
        });
        return done(null, user);
      }

      // Create new user
      const newUser = new UserModel({
        telegramId: profile.id.toString(),
        telegramUsername: profile.username,
        username: profile.username || `telegram_${profile.id}`,
        email: `telegram_${profile.id}@telegram.local`, // Placeholder email
        name: `${profile.first_name} ${profile.last_name || ''}`.trim(),
        provider: 'telegram',
        profilePicture: profile.photo_url,
        avatar: profile.photo_url,
        status: 1,
        lastLoginAt: new Date(),
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      await newUser.save();
      done(null, newUser);
    } catch (error) {
      console.error('Telegram OAuth error:', error);
      done(error, null);
    }
  }));
}

module.exports = passport;
