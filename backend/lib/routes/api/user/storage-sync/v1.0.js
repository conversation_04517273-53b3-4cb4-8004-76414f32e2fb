const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const storageService = require('../../../../services/storageService');

module.exports = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'User authentication required'
      });
    }

    // Sync user storage statistics
    const updatedStorageInfo = await storageService.syncUserStorageStats(userId);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: updatedStorageInfo,
      message: 'Storage statistics synchronized successfully'
    });

  } catch (error) {
    global.logger.logInfo(['user/storage-sync error', error.message], __dirname);
    console.error('Storage sync error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: error.message || MESSAGES.SYSTEM.ERROR
    });
  }
};
