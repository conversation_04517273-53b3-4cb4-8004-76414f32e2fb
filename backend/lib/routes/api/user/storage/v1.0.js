const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const storageService = require('../../../../services/storageService');

module.exports = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'User authentication required'
      });
    }

    // Get user storage information
    const storageInfo = await storageService.getUserStorageInfo(userId);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: storageInfo,
      message: 'Storage information retrieved successfully'
    });

  } catch (error) {
    global.logger.logInfo(['user/storage error', error.message], __dirname);
    console.error('Storage info error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: error.message || MESSAGES.SYSTEM.ERROR
    });
  }
};
