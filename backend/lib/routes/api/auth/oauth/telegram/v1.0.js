const passport = require('passport');
const config = require('config');
const crypto = require('crypto');
const CONSTANTS = require('../../../../../const');
const MESSAGES = require('../../../../../message');

module.exports = {
  // Initiate Telegram OAuth
  login: (req, res, next) => {
    if (!config.get('oauth.enabled') || !config.get('oauth.telegram.enabled')) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Telegram OAuth is not enabled'
      });
    }

    passport.authenticate('telegram')(req, res, next);
  },

  // Handle Telegram OAuth callback
  callback: (req, res, next) => {
    if (!config.get('oauth.enabled') || !config.get('oauth.telegram.enabled')) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Telegram OAuth is not enabled'
      });
    }

    passport.authenticate('telegram', { 
      failureRedirect: '/login?error=telegram_auth_failed',
      session: false 
    }, async (err, user, info) => {
      if (err) {
        console.error('Telegram OAuth callback error:', err);
        return res.redirect('/login?error=oauth_error');
      }

      if (!user) {
        return res.redirect('/login?error=telegram_auth_failed');
      }

      try {
        // Generate JWT token
        const jwt = require('jsonwebtoken');
        const redisConnection = require('../../../../../connections/redis');
        
        const token = jwt.sign({ 
          username: user.username, 
          id: user._id 
        }, config.get('secretKey'));
        
        const userId = user._id.toString();
        const objSign = {
          id: userId,
          role: user.role,
        };

        // Store token in Redis
        await new Promise((resolve, reject) => {
          redisConnection('master')
            .getConnection()
            .multi()
            .set(`user:${userId}`, token)
            .set(`user:${token}`, JSON.stringify(objSign))
            .exec((err, result) => {
              if (err) reject(err);
              else resolve(result);
            });
        });

        // Redirect to frontend with token
        res.redirect(`/login-success?token=${token}&user=${encodeURIComponent(JSON.stringify({
          id: user._id,
          username: user.username,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          provider: user.provider
        }))}`);

      } catch (error) {
        console.error('Token generation error:', error);
        res.redirect('/login?error=token_error');
      }
    })(req, res, next);
  },

  // Verify Telegram login widget data
  verifyWidget: async (req, res) => {
    if (!config.get('oauth.enabled') || !config.get('oauth.telegram.enabled')) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Telegram OAuth is not enabled'
      });
    }

    try {
      const { hash, ...data } = req.body;
      
      if (!hash) {
        return res.status(400).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Hash is required'
        });
      }

      // Verify Telegram data
      const botToken = config.get('oauth.telegram.botToken');
      const secretKey = crypto.createHash('sha256').update(botToken).digest();
      
      const dataCheckString = Object.keys(data)
        .sort()
        .map(key => `${key}=${data[key]}`)
        .join('\n');
      
      const hmac = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');
      
      if (hmac !== hash) {
        return res.status(400).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Invalid Telegram data'
        });
      }

      // Check if auth_date is not too old (within 1 hour)
      const authDate = parseInt(data.auth_date);
      const currentTime = Math.floor(Date.now() / 1000);
      if (currentTime - authDate > 3600) {
        return res.status(400).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'Telegram auth data is too old'
        });
      }

      // Find or create user
      const UserModel = require('../../../../../models/user');
      let user = await UserModel.findOne({ telegramId: data.id.toString() });
      
      if (!user) {
        // Create new user
        user = new UserModel({
          telegramId: data.id.toString(),
          telegramUsername: data.username,
          username: data.username || `telegram_${data.id}`,
          email: `telegram_${data.id}@telegram.local`,
          name: `${data.first_name} ${data.last_name || ''}`.trim(),
          provider: 'telegram',
          profilePicture: data.photo_url,
          avatar: data.photo_url,
          status: 1,
          lastLoginAt: new Date(),
          createdAt: Date.now(),
          updatedAt: Date.now()
        });
        await user.save();
      } else {
        // Update last login
        user.lastLoginAt = new Date();
        user.updatedAt = Date.now();
        await UserModel.updateOne({ _id: user._id }, { 
          lastLoginAt: user.lastLoginAt,
          updatedAt: user.updatedAt 
        });
      }

      // Generate JWT token
      const jwt = require('jsonwebtoken');
      const redisConnection = require('../../../../../connections/redis');
      
      const token = jwt.sign({ 
        username: user.username, 
        id: user._id 
      }, config.get('secretKey'));
      
      const userId = user._id.toString();
      const objSign = {
        id: userId,
        role: user.role,
      };

      // Store token in Redis
      await new Promise((resolve, reject) => {
        redisConnection('master')
          .getConnection()
          .multi()
          .set(`user:${userId}`, token)
          .set(`user:${token}`, JSON.stringify(objSign))
          .exec((err, result) => {
            if (err) reject(err);
            else resolve(result);
          });
      });

      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: {
          token,
          user: {
            id: user._id,
            username: user.username,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            provider: user.provider
          }
        },
        message: 'Telegram login successful'
      });

    } catch (error) {
      console.error('Telegram widget verification error:', error);
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  }
};
