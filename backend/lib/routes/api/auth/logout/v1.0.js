const _ = require('lodash');
const redisConnection = require('../../../../connections/redis');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const token = _.get(req, 'headers.token', '');
    const userId = _.get(req, 'user.id', '');

    if (!token || !userId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    try {
      const redis = redisConnection('master').getConnection();

      // Remove token from Redis
      await redis.del(`user:${userId}`);
      await redis.del(`user:${token}`);

      console.log('✅ Token removed from Redis successfully');
    } catch (redisError) {
      console.error('Redis deletion error:', redisError);
      // Continue anyway - token will expire naturally
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
