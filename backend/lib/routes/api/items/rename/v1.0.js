const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const { id } = req.params;
    const { name: newName } = req.body;

    if (!id) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Item ID is required'
      });
    }

    if (!newName || typeof newName !== 'string' || newName.trim().length === 0) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'New name is required and must be a non-empty string'
      });
    }

    // Validate name (no special characters for folders)
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(newName.trim())) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Name contains invalid characters'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Try to find as file first
    let item = await FileModel.findOne({
      _id: id,
      isDeleted: false
    });

    let itemType = 'file';
    let updateResult;

    if (item) {
      // It's a file - update originalFileName
      updateResult = await FileModel.updateOne(
        { _id: id, isDeleted: false },
        { originalFileName: newName.trim() }
      );
    } else {
      // Try to find as folder
      item = await FolderModel.findOne({
        _id: id,
        isDeleted: false
      });

      if (!item) {
        return res.status(404).json({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Item not found'
        });
      }

      itemType = 'folder';

      // Check for duplicate folder name in same parent
      const duplicateFolder = await FolderModel.findOne({
        folderName: newName.trim(),
        parentId: item.parentId,
        isDeleted: false,
        _id: { $ne: id }
      });

      if (duplicateFolder) {
        return res.status(400).json({
          code: CONSTANTS.CODE.INVALID_PARAMS,
          message: 'A folder with this name already exists in the current location'
        });
      }

      // Update folder name
      updateResult = await FolderModel.updateOne(
        { _id: id, isDeleted: false },
        { folderName: newName.trim() }
      );
    }

    if (updateResult.matchedCount === 0) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'Item not found'
      });
    }

    // Clear cache for parent folder
    try {
      await cacheService.clearFolderCache(item.parentId);
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`${itemType} renamed successfully: ${item[itemType === 'file' ? 'originalFileName' : 'folderName']} -> ${newName.trim()}, ID: ${id}`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: id,
        newName: newName.trim(),
        type: itemType,
        parentId: item.parentId
      },
      message: `${itemType === 'file' ? 'File' : 'Folder'} renamed successfully`
    });

  } catch (error) {
    global.logger.logInfo(['items/rename error', error.message], __dirname);
    console.error('Rename error:', error);

    // Handle duplicate key error for folders
    if (error.code === 11000) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'A folder with this name already exists in the current location'
      });
    } else {
      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  }
};
