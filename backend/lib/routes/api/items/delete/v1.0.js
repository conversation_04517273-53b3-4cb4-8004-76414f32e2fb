const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const cacheService = require('../../../../services/cache');
const storageService = require('../../../../services/storageService');

module.exports = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'Item ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    // Try to find as file first
    let item = await FileModel.findOne({
      _id: id,
      isDeleted: false
    });

    let itemType = 'file';
    let deletedCount = 0;

    if (item) {
      // It's a file - soft delete and update storage
      await FileModel.updateOne(
        { _id: id, isDeleted: false },
        {
          isDeleted: true,
          deletedAt: new Date()
        }
      );

      // Update user storage usage
      if (item.ownerId && item.fileSize) {
        try {
          await storageService.removeStorageUsage(item.ownerId, item.fileSize);
        } catch (storageError) {
          console.warn('Failed to update storage usage on file deletion:', storageError.message);
        }
      }

      deletedCount = 1;
    } else {
      // Try to find as folder
      item = await FolderModel.findOne({
        _id: id,
        isDeleted: false
      });

      if (!item) {
        return res.status(404).json({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: 'Item not found'
        });
      }

      itemType = 'folder';

      // Recursively delete all contents
      const deleteResults = await deleteFolder(id);
      deletedCount = deleteResults.totalDeleted;
    }

    // Clear cache for parent folder and search
    try {
      await cacheService.clearFolderCache(item.parentId);
      await cacheService.clearSearchCache();
    } catch (cacheError) {
      console.warn('Cache clear failed:', cacheError.message);
    }

    global.logger.logInfo(`${itemType} deleted successfully: ID ${id}, ${deletedCount} items deleted`);

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        id: id,
        type: itemType,
        deletedCount: deletedCount
      },
      message: `${itemType === 'file' ? 'File' : 'Folder'} deleted successfully`
    });

  } catch (error) {
    global.logger.logInfo(['items/delete error', error.message], __dirname);
    console.error('Delete error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }

  // Helper function to recursively delete folder and its contents
  async function deleteFolder(folderId) {
    const FileModel = require('../../../../models/file');
    const FolderModel = require('../../../../models/folder');

    let totalDeleted = 0;
    let totalStorageFreed = '0'; // Use string for large numbers
    let totalFilesDeleted = 0;
    let totalFoldersDeleted = 0;
    let ownerId = null;

    // Get all subfolders
    const subfolders = await FolderModel.find({
      parentId: folderId,
      isDeleted: false
    }).select('_id ownerId').lean();

    // Recursively delete subfolders
    for (const subfolder of subfolders) {
      const subResult = await deleteFolder(subfolder._id);
      totalDeleted += subResult.totalDeleted;
      totalStorageFreed = storageService.addStringNumbers(totalStorageFreed, subResult.totalStorageFreed);
      totalFilesDeleted += subResult.totalFilesDeleted;
      totalFoldersDeleted += subResult.totalFoldersDeleted;
      if (!ownerId && subfolder.ownerId) {
        ownerId = subfolder.ownerId;
      }
    }

    // Get all files in this folder to calculate storage freed
    const filesToDelete = await FileModel.find({
      parentId: folderId,
      isDeleted: false
    }).select('fileSize ownerId').lean();

    // Calculate total storage to be freed
    for (const file of filesToDelete) {
      if (file.fileSize) {
        totalStorageFreed = storageService.addStringNumbers(totalStorageFreed, file.fileSize.toString());
      }
      totalFilesDeleted += 1;
      if (!ownerId && file.ownerId) {
        ownerId = file.ownerId;
      }
    }

    // Delete all files in this folder
    const fileDeleteResult = await FileModel.updateMany(
      { parentId: folderId, isDeleted: false },
      {
        isDeleted: true,
        deletedAt: new Date()
      }
    );
    totalDeleted += fileDeleteResult.modifiedCount;

    // Delete all subfolders in this folder
    const subfoldersToDelete = await FolderModel.find({
      parentId: folderId,
      isDeleted: false
    }).select('_id folderName ownerId').lean();

    for (const subfolder of subfoldersToDelete) {
      const timestamp = Date.now();
      const uniqueSuffix = `_deleted_${timestamp}_${Math.random().toString(36).substring(2, 11)}`;

      await FolderModel.updateOne(
        { _id: subfolder._id, isDeleted: false },
        {
          folderName: `${subfolder.folderName}${uniqueSuffix}`,
          isDeleted: true,
          deletedAt: new Date()
        }
      );
      totalDeleted += 1;
      totalFoldersDeleted += 1;
      if (!ownerId && subfolder.ownerId) {
        ownerId = subfolder.ownerId;
      }
    }

    // Delete the folder itself
    const folderToDelete = await FolderModel.findOne({
      _id: folderId,
      isDeleted: false
    }).select('folderName ownerId').lean();

    if (folderToDelete) {
      const timestamp = Date.now();
      const uniqueSuffix = `_deleted_${timestamp}_${Math.random().toString(36).substring(2, 11)}`;

      const mainFolderResult = await FolderModel.updateOne(
        { _id: folderId, isDeleted: false },
        {
          folderName: `${folderToDelete.folderName}${uniqueSuffix}`,
          isDeleted: true,
          deletedAt: new Date()
        }
      );
      totalDeleted += mainFolderResult.modifiedCount;
      totalFoldersDeleted += mainFolderResult.modifiedCount;
      if (!ownerId && folderToDelete.ownerId) {
        ownerId = folderToDelete.ownerId;
      }
    }

    // Update user storage usage if we have an owner and freed storage
    if (ownerId && (storageService.compareStringNumbers(totalStorageFreed, '0') > 0 || totalFoldersDeleted > 0)) {
      try {
        // Update storage and file count
        if (storageService.compareStringNumbers(totalStorageFreed, '0') > 0) {
          await storageService.removeStorageUsageBulk(ownerId, totalStorageFreed, totalFilesDeleted);
        }
        // Update folder count separately if needed
        if (totalFoldersDeleted > 0) {
          await storageService.removeFolderCount(ownerId, totalFoldersDeleted);
        }
      } catch (storageError) {
        console.warn('Failed to update storage usage on folder deletion:', storageError.message);
      }
    }

    return {
      totalDeleted,
      totalStorageFreed,
      totalFilesDeleted,
      totalFoldersDeleted
    };
  }
};
