const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`📥 DOWNLOAD REQUEST: File ID: ${fileId}`);
    console.log(`📥 DOWNLOAD REQUEST: User: ${req.user ? req.user.id : 'No user'}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: 'Authentication required'
      });
    }

    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false,
      ownerId: userId // Only allow user's own files
    }).lean();

    console.log(`📥 DOWNLOAD: File found: ${file ? 'Yes' : 'No'}`);
    if (file) {
      console.log(`📥 DOWNLOAD: File name: ${file.originalFileName}, Size: ${file.fileSize}`);
    }

    if (!file) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found or access denied'
      });
    }

    console.log(`📥 DOWNLOAD: Getting file from Telegram: ${file.telegramFileId}`);

    try {
      // Get file buffer directly from Telegram instead of streaming
      const fileBuffer = await telegramService.downloadFile(file.telegramFileId);

      console.log(`📥 DOWNLOAD: File buffer size: ${fileBuffer.length} bytes`);
      console.log(`📥 DOWNLOAD: Expected size: ${file.fileSize} bytes`);

      // Set response headers
      res.setHeader('Content-Type', file.mimeType || 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.originalFileName)}"`);
      res.setHeader('Content-Length', fileBuffer.length);
      res.setHeader('Cache-Control', 'no-cache');

      // Send the buffer directly
      res.send(fileBuffer);

      console.log(`📥 DOWNLOAD SUCCESS: ${file.originalFileName}`);

    } catch (telegramError) {
      console.error('📥 DOWNLOAD ERROR: Telegram download failed:', telegramError);
      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to download file from storage'
      });
    }

  } catch (error) {
    console.error('📥 DOWNLOAD ERROR: General error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to download file'
      });
    }
  }
};
