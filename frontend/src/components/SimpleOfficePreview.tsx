import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { fileApi } from '../services/api';
import { formatFileSize, downloadFile } from '../utils/helpers';

interface SimpleOfficePreviewProps {
  item: any;
  onLoadComplete: () => void;
}

const SimpleOfficePreview: React.FC<SimpleOfficePreviewProps> = ({ item, onLoadComplete }) => {
  const { t } = useTranslation();
  const [viewMode, setViewMode] = useState<'info' | 'embedded'>('info');
  const [viewerLoading, setViewerLoading] = useState(false);
  const [viewerError, setViewerError] = useState(false);
  const [currentViewer, setCurrentViewer] = useState<'google' | 'microsoft'>('google');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    onLoadComplete();
  }, [onLoadComplete]);

  // Get file type info for better display
  const getFileTypeInfo = () => {
    const mimeType = item.mimeType.toLowerCase();
    if (mimeType.includes('word') || mimeType.includes('document')) {
      return { icon: '📝', color: '#2196F3', type: 'Word Document' };
    } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
      return { icon: '📊', color: '#4CAF50', type: 'Excel Spreadsheet' };
    } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
      return { icon: '📽️', color: '#FF9800', type: 'PowerPoint Presentation' };
    } else if (mimeType.includes('pdf')) {
      return { icon: '📄', color: '#F44336', type: 'PDF Document' };
    }
    return { icon: '📄', color: '#2196F3', type: 'Office Document' };
  };

  const fileTypeInfo = getFileTypeInfo();

  const handleEmbeddedViewer = () => {
    setViewMode('embedded');
    setViewerLoading(true);
    setViewerError(false);
    setErrorMessage('');
  };

  const handleExternalViewer = () => {
    setViewerLoading(true);
    setViewerError(false);
    setErrorMessage('');

    const fileUrl = fileApi.previewFile(item.id);
    let viewerUrl = '';

    if (currentViewer === 'google') {
      viewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`;
    } else {
      viewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
    }

    const newWindow = window.open(viewerUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

    setTimeout(() => {
      setViewerLoading(false);
      if (!newWindow || newWindow.closed) {
        setViewerError(true);
        setErrorMessage('Unable to open viewer. Please check your popup blocker settings.');
      }
    }, 1000);
  };

  const tryAlternativeViewer = () => {
    if (currentViewer === 'google') {
      setCurrentViewer('microsoft');
      setViewerError(false);
      setErrorMessage('');
      setViewerLoading(true);
      setTimeout(() => handleEmbeddedViewer(), 500);
    } else {
      setViewerError(true);
      setErrorMessage('Unable to preview this document. Please download it to view.');
    }
  };

  if (viewMode === 'embedded') {
    const fileUrl = fileApi.previewFile(item.id);
    let viewerUrl = '';
    let viewerName = '';

    if (currentViewer === 'google') {
      viewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`;
      viewerName = 'Google Docs Viewer';
    } else {
      viewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
      viewerName = 'Microsoft Office Online';
    }

    return (
      <Box
        sx={{
          width: '100%',
          height: '70vh',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {viewerLoading && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
              bgcolor: 'background.paper',
              p: 3,
              borderRadius: 2,
              boxShadow: 2,
            }}
          >
            <CircularProgress />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Loading document with {viewerName}...
            </Typography>
          </Box>
        )}

        <iframe
          src={viewerUrl}
          sandbox="allow-same-origin allow-scripts allow-forms allow-downloads allow-popups"
          allow="fullscreen"
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            opacity: viewerLoading ? 0.3 : 1,
            transition: 'opacity 0.3s ease',
          }}
          title={item.name}
          onLoad={() => {
            console.log(`Office document loaded with ${viewerName}`);
            setTimeout(() => {
              setViewerLoading(false);
            }, 3000);
          }}
          onError={() => {
            console.error(`Office document failed to load with ${viewerName}`);
            setViewerLoading(false);
            setViewerError(true);
            setErrorMessage(`Failed to load with ${viewerName}. Try alternative viewer or download.`);
          }}
        />

        {viewerError && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              zIndex: 2,
              bgcolor: 'background.paper',
              p: 3,
              borderRadius: 2,
              boxShadow: 2,
              maxWidth: '80%',
            }}
          >
            <Typography variant="h6" color="error" gutterBottom>
              Preview Failed
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {errorMessage}
            </Typography>

            <Box sx={{ mt: 2, display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={tryAlternativeViewer}
                disabled={viewerLoading}
              >
                Try Alternative Viewer
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setViewMode('info')}
              >
                Back
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => {
                  const downloadUrl = fileApi.downloadFile(item.id);
                  downloadFile(downloadUrl, item.name);
                }}
              >
                Download
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box sx={{ textAlign: 'center', py: 4, width: '100%' }}>
      <Box sx={{ fontSize: 64, mb: 3, color: fileTypeInfo.color }}>
        {fileTypeInfo.icon}
      </Box>
      <Typography variant="h6" gutterBottom>
        {item.name}
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {fileTypeInfo.type}
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Size: {formatFileSize(item.size)}
      </Typography>

      {viewerError && (
        <Alert severity="error" sx={{ mt: 2, mb: 2, maxWidth: 400, mx: 'auto' }}>
          {errorMessage || 'Unable to open viewer. Please try downloading the file.'}
        </Alert>
      )}

      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
        <Button
          variant="outlined"
          onClick={handleEmbeddedViewer}
          disabled={viewerLoading}
          sx={{ minWidth: 160 }}
          startIcon={viewerLoading ? <CircularProgress size={16} /> : null}
        >
          {viewerLoading ? t('common.loading') : t('preview.previewHere')}
        </Button>
        <Button
          variant="outlined"
          onClick={handleExternalViewer}
          disabled={viewerLoading}
          sx={{ minWidth: 160 }}
        >
          {viewerLoading ? (
            <>
              <CircularProgress size={16} sx={{ mr: 1 }} />
              {t('preview.opening')}
            </>
          ) : (
            t('preview.openInNewTab')
          )}
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            const downloadUrl = fileApi.downloadFile(item.id);
            downloadFile(downloadUrl, item.name);
          }}
          sx={{ minWidth: 120 }}
        >
          {t('common.download')}
        </Button>
      </Box>

      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1, maxWidth: 500, mx: 'auto' }}>
        <Typography variant="caption" color="info.dark" sx={{ display: 'block', mb: 1 }}>
          <strong>{t('preview.previewOptions')}:</strong>
        </Typography>
        <Typography variant="caption" color="info.dark" sx={{ display: 'block' }}>
          • {t('preview.googleDocsViewer')} • {t('preview.microsoftOffice')} • {t('preview.downloadLocal')}
        </Typography>
        {currentViewer === 'microsoft' && (
          <Typography variant="caption" color="warning.dark" sx={{ display: 'block', mt: 1 }}>
            {t('preview.currentlyUsing')}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default SimpleOfficePreview;
