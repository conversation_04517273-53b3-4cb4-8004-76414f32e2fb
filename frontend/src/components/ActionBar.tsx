import React from 'react';
import {
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  CloudUpload as UploadIcon,
  CreateNewFolder as FolderIcon,
  GetApp as GetAppIcon,
  MoreVert as MoreIcon,
  Link as LinkIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';

interface ActionBarProps {
  onUploadClick: () => void;
  onCreateFolderClick: () => void;
  currentPath?: string;
}

const ActionBar: React.FC<ActionBarProps> = ({
  onUploadClick,
  onCreateFolderClick,
  currentPath = 'Home',
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMoreClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // const suggestions = [
  //   'HeyU-ver...DjwFVk+',
  //   'HeyU-ver...PPTlkA+',
  //   'HeyU-ver...RQShl+',
  //   'HeyU-ver...4RhLh4+',
  //   'HeyU-ver...PX+30+',
  // ];

  return (
    <Box>
      {/* Main Action Bar */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          py: 2,
          px: 3,
          backgroundColor: theme.palette.background.paper,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: isMobile ? 1 : 2,
          flexWrap: isMobile ? 'wrap' : 'nowrap',
          width: '100%'
        }}>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={onUploadClick}
            sx={{
              backgroundColor: '#0061FF',
              color: 'white',
              fontWeight: 500,
              px: isMobile ? 2 : 3,
              py: 1,
              fontSize: isMobile ? '0.875rem' : '1rem',
              minWidth: isMobile ? 'auto' : 'unset',
              '&:hover': {
                backgroundColor: '#0047CC',
              },
            }}
          >
            {isMobile ? t('actionBar.upload') : t('actionBar.uploadFiles')}
          </Button>

          <Button
            variant="outlined"
            startIcon={<FolderIcon />}
            onClick={onCreateFolderClick}
            sx={{
              borderColor: theme.palette.divider,
              color: theme.palette.text.primary,
              fontWeight: 500,
              px: isMobile ? 1.5 : 2,
              py: 1,
              fontSize: isMobile ? '0.875rem' : '1rem',
              minWidth: isMobile ? 'auto' : 'unset',
              '&:hover': {
                borderColor: theme.palette.divider,
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            {isMobile ? t('actionBar.folder') : t('actionBar.createFolder')}
          </Button>

          {!isMobile && (
            <>
              <Button
                variant="outlined"
                startIcon={<GetAppIcon />}
                disabled
                sx={{
                  borderColor: theme.palette.divider,
                  color: theme.palette.text.disabled,
                  fontWeight: 500,
                  px: 2,
                  py: 1,
                }}
              >
                {t('actionBar.getApp')}
              </Button>

              <Button
                variant="outlined"
                disabled
                sx={{
                  borderColor: '#E5E7EB',
                  color: '#9CA3AF',
                  fontWeight: 500,
                  px: 2,
                  py: 1,
                }}
              >
                {t('actionBar.transferCopy')}
              </Button>

              <Button
                variant="outlined"
                disabled
                sx={{
                  borderColor: '#E5E7EB',
                  color: '#9CA3AF',
                  fontWeight: 500,
                  px: 2,
                  py: 1,
                }}
              >
                {t('actionBar.share')}
              </Button>
            </>
          )}
        </Box>

        <IconButton
          onClick={handleMoreClick}
          sx={{
            color: '#637381',
            '&:hover': {
              backgroundColor: '#F3F4F6',
            },
          }}
        >
          <MoreIcon />
        </IconButton>

        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          slotProps={{
            paper: {
              sx: {
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                border: '1px solid #E5E7EB',
                borderRadius: 2,
                mt: 1,
              },
            },
          }}
        >
          <MenuItem onClick={handleClose} disabled>
            <ListItemIcon>
              <LinkIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>{t('actionBar.copyLink')}</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleClose} disabled>
            <ListItemIcon>
              <ShareIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>{t('actionBar.shareFolder')}</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleClose} disabled>
            <ListItemIcon>
              <DownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>{t('actionBar.download')}</ListItemText>
          </MenuItem>
        </Menu>
      </Box>

      {/* Suggested for you section */}
      {/* <Box
        sx={{
          py: 2,
          px: 3,
          backgroundColor: '#FFFFFF',
          borderBottom: '1px solid #E5E7EB',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: '1rem',
              color: '#1E1E1E',
              mr: 1,
            }}
          >
            Suggested for you
          </Typography>
          <IconButton size="small" sx={{ color: '#637381' }}>
            <MoreIcon fontSize="small" />
          </IconButton>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, overflowX: 'auto', pb: 1 }}>
          {suggestions.map((suggestion, index) => (
            <Box
              key={index}
              sx={{
                minWidth: 120,
                height: 80,
                backgroundColor: '#4285FF',
                borderRadius: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.02)',
                },
              }}
            >
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1,
                }}
              >
                <FolderIcon sx={{ color: 'white', fontSize: 20 }} />
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: 'white',
                  fontSize: '0.75rem',
                  textAlign: 'center',
                  px: 1,
                }}
              >
                {suggestion}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box> */}
    </Box>
  );
};

export default ActionBar;
