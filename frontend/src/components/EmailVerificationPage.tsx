import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import './EmailVerificationPage.css';

const EmailVerificationPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const token = searchParams.get('token');

    if (!token) {
      setStatus('error');
      setMessage(t('emailVerification.invalidLink'));
      return;
    }

    // Call verification API
    const verifyEmail = async () => {
      try {
        const response = await fetch(`/api/v1.0/auth/verify-email?token=${token}`);
        const data = await response.json();

        if (data.code === 200) {
          setStatus('success');
          setMessage(data.message || t('emailVerification.verificationSuccess'));

          // Redirect to login after 3 seconds
          setTimeout(() => {
            navigate('/login');
          }, 3000);
        } else {
          setStatus('error');
          setMessage(data.message || t('emailVerification.verificationFailed'));
        }
      } catch (error) {
        setStatus('error');
        setMessage(t('emailVerification.networkError'));
      }
    };

    verifyEmail();
  }, [searchParams, navigate]);

  return (
    <div className="email-verification-page">
      <div className="verification-container">
        <div className="verification-content">
          {status === 'loading' && (
            <>
              <div className="spinner"></div>
              <h2>{t('emailVerification.verifying')}</h2>
              <p>{t('emailVerification.verifyingMessage')}</p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="success-icon">✅</div>
              <h2>{t('emailVerification.verified')}</h2>
              <p>{message}</p>
              <p>{t('emailVerification.redirectMessage')}</p>
              <button
                className="login-button"
                onClick={() => navigate('/login')}
              >
                {t('emailVerification.goToLogin')}
              </button>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="error-icon">❌</div>
              <h2>{t('emailVerification.verificationFailed')}</h2>
              <p>{message}</p>
              <button
                className="login-button"
                onClick={() => navigate('/login')}
              >
                {t('emailVerification.backToLogin')}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationPage;
