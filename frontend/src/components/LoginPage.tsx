import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Tab,
  Tabs,
  Alert,
  LinearProgress,
  FormHelperText,
  useTheme,
  ThemeProvider,
  createTheme,
  CssBaseline,
  useMediaQuery,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import {
  validatePassword,
  getPasswordStrengthText,
} from "../utils/passwordValidation";
import { useTranslation } from "react-i18next";

interface LoginPageProps {
  onLogin: (token: string, user: any) => void;
}

const LoginPageNew: React.FC<LoginPageProps> = ({ onLogin }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();
  const prefersDarkMode = useMediaQuery("(prefers-color-scheme: dark)");

  // Initialize theme from localStorage first, then fallback to system preference
  const getInitialTheme = (): "light" | "dark" => {
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme === "light" || savedTheme === "dark") {
      return savedTheme;
    }
    return prefersDarkMode ? "dark" : "light";
  };

  const [mode] = useState<"light" | "dark">(getInitialTheme());
  const [tabValue, setTabValue] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<any>(null);
  const [passwordMatch, setPasswordMatch] = useState<boolean | null>(null);
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    name: "",
    phone: "",
  });

  const theme = createTheme({
    palette: {
      mode,
      primary: {
        main: mode === "dark" ? "#60a5fa" : "#2563eb",
        light: mode === "dark" ? "#93c5fd" : "#3b82f6",
        dark: mode === "dark" ? "#2563eb" : "#1d4ed8",
      },
      background: {
        default: mode === "dark" ? "#0f172a" : "#f8fafc",
        paper: mode === "dark" ? "#1e293b" : "#ffffff",
      },
      text: {
        primary: mode === "dark" ? "#f1f5f9" : "#1e293b",
        secondary: mode === "dark" ? "#cbd5e1" : "#64748b",
      },
    },
  });

  useEffect(() => {
    // Check for OAuth errors in URL params
    const error = searchParams.get("error");
    if (error) {
      setError(decodeURIComponent(error));
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear errors when user starts typing
    if (error) setError("");
    if (success) setSuccess("");

    // Check password strength for register mode
    if (name === "password" && tabValue === 1) {
      if (value.length > 0) {
        const strength = validatePassword(value);
        setPasswordStrength(strength);
      } else {
        setPasswordStrength(null);
      }

      // Check password match if confirm password is filled
      if (formData.confirmPassword) {
        setPasswordMatch(value === formData.confirmPassword);
      }
    }

    // Check password confirmation match
    if (name === "confirmPassword" && tabValue === 1) {
      if (value.length > 0) {
        setPasswordMatch(value === formData.password);
      } else {
        setPasswordMatch(null);
      }
    }
  };

  const validateForm = (): boolean => {
    if (tabValue === 0) {
      // Login
      if (!formData.username || !formData.password) {
        setError("Please fill in all required fields");
        return false;
      }
    } else {
      // Register
      if (
        !formData.username ||
        !formData.password ||
        !formData.email ||
        !formData.name
      ) {
        setError("Please fill in all required fields");
        return false;
      }

      if (formData.password.length < 6) {
        setError(t("auth.passwordTooShort"));
        return false;
      }

      if (formData.password !== formData.confirmPassword) {
        setError(t("auth.passwordMismatch"));
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setError("");
    setSuccess("");

    try {
      const endpoint =
        tabValue === 0 ? "/api/v1.0/auth/login" : "/api/v1.0/auth/register";
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.code === 200) {
        if (tabValue === 0) {
          // Login
          onLogin(data.data.token, data.data);
          navigate("/");
        } else {
          // Register
          setSuccess(t("auth.registerSuccess"));
          setTabValue(0); // Switch to login tab
          setFormData((prev) => ({
            ...prev,
            password: "",
            confirmPassword: "",
          }));
        }
      } else {
        setError(data.message?.body || data.message || "Authentication failed");
      }
    } catch (error) {
      setError("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          bgcolor: "background.default",
          p: 2,
        }}
      >
        <Paper
          sx={{
            p: 4,
            maxWidth: 400,
            width: "100%",
            borderRadius: 2,
            boxShadow:
              mode === "dark"
                ? "0 4px 20px rgba(0,0,0,0.3)"
                : "0 4px 20px rgba(0,0,0,0.1)",
          }}
        >
          {/* Header */}
          <Box sx={{ textAlign: "center", mb: 3 }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                mb: 1,
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              TeleStore
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t("header.tagline")}
            </Typography>
          </Box>

          {/* Error/Success Messages */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {/* Tabs */}
          <Tabs
            value={tabValue}
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="fullWidth"
            sx={{ mb: 3 }}
          >
            <Tab label={t("auth.login")} />
            <Tab label={t("auth.register")} />
          </Tabs>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label={t("auth.username")}
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              margin="normal"
              required
            />

            {tabValue === 1 && (
              <>
                <TextField
                  fullWidth
                  label={t("auth.email")}
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                />
                <TextField
                  fullWidth
                  label={t("auth.name")}
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                />
                <TextField
                  fullWidth
                  label={t("auth.phone")}
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  margin="normal"
                />
              </>
            )}

            <TextField
              fullWidth
              label={t("auth.password")}
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={handleInputChange}
              margin="normal"
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Password Strength Indicator */}
            {tabValue === 1 && passwordStrength && (
              <Box sx={{ mt: 1, mb: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={(passwordStrength.score / 4) * 100}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: "rgba(0,0,0,0.1)",
                    "& .MuiLinearProgress-bar": {
                      backgroundColor: passwordStrength.color,
                      borderRadius: 3,
                    },
                  }}
                />
                <FormHelperText sx={{ color: passwordStrength.color, mt: 0.5 }}>
                  {getPasswordStrengthText(passwordStrength.label)}
                  {passwordStrength.feedback.length > 0 && (
                    <span> - {passwordStrength.feedback[0]}</span>
                  )}
                </FormHelperText>
              </Box>
            )}

            {tabValue === 1 && (
              <>
                <TextField
                  fullWidth
                  label={t("auth.confirmPassword")}
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                  error={passwordMatch === false}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          edge="end"
                        >
                          {showConfirmPassword ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                {passwordMatch === false && (
                  <FormHelperText error sx={{ mt: 0.5 }}>
                    {t("auth.passwordMismatch")}
                  </FormHelperText>
                )}
                {passwordMatch === true && (
                  <FormHelperText sx={{ mt: 0.5, color: "success.main" }}>
                    Passwords match ✓
                  </FormHelperText>
                )}
              </>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {isLoading
                ? t("auth.processing")
                : tabValue === 0
                ? t("auth.login")
                : t("auth.register")}
            </Button>
          </form>
        </Paper>
      </Box>
    </ThemeProvider>
  );
};

export default LoginPageNew;
