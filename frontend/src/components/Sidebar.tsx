import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Chip,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  Home as HomeIcon,
  Folder as FolderIcon,
  Image as ImageIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  Delete as DeleteIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import StorageIndicator from './StorageIndicator';

interface SidebarProps {
  onNavigate: (folderId: string | null) => void;
  currentFolderId: string | null;
}

const getMenuItems = (t: any) => [
  {
    id: 'home',
    label: t('sidebar.home'),
    icon: <HomeIcon />,
    folderId: null,
    isActive: (currentFolderId: string | null, activeItemId: string | null) =>
      currentFolderId === null && (activeItemId === null || activeItemId === 'home'),
  },
  {
    id: 'files',
    label: t('sidebar.allFiles'),
    icon: <FolderIcon />,
    folderId: null,
    isActive: (currentFolderId: string | null, activeItemId: string | null) =>
      currentFolderId === null && activeItemId === 'files',
  },
  {
    id: 'photos',
    label: t('sidebar.photos'),
    icon: <ImageIcon />,
    folderId: null,
    disabled: true,
    isActive: () => false,
  },
  {
    id: 'shared',
    label: t('sidebar.shared'),
    icon: <PeopleIcon />,
    folderId: null,
    disabled: true,
    isActive: () => false,
  },
  {
    id: 'requests',
    label: t('sidebar.fileRequests'),
    icon: <AssignmentIcon />,
    folderId: null,
    disabled: true,
    isActive: () => false,
  },
  {
    id: 'deleted',
    label: t('sidebar.deletedFiles'),
    icon: <DeleteIcon />,
    folderId: null,
    disabled: true,
    isActive: () => false,
  },
];

const getQuickAccessItems = (t: any) => [
  {
    id: 'starred',
    label: t('sidebar.starred'),
    icon: <StarIcon />,
    disabled: true,
  },
  {
    id: 'recent',
    label: t('sidebar.recent'),
    icon: <ScheduleIcon />,
    disabled: true,
  },
];

const Sidebar: React.FC<SidebarProps> = ({ onNavigate, currentFolderId }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const [activeItemId, setActiveItemId] = useState<string | null>('home');

  const menuItems = getMenuItems(t);
  const quickAccessItems = getQuickAccessItems(t);

  const handleItemClick = (item: any) => {
    if (!item.disabled) {
      setActiveItemId(item.id);
      onNavigate(item.folderId);
    }
  };

  return (
    <Box
      sx={{
        width: 260,
        height: "calc(100vh - 64px)",
        backgroundColor: theme.palette.background.paper,
        borderRight: `1px solid ${theme.palette.divider}`,
        display: "flex",
        flexDirection: "column",
        position: "fixed",
        top: "64px",
        left: 0,
        zIndex: 1000,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        overflow: "hidden", // Prevent outer scrolling
      }}
    >
      {/* Scrollable content area */}
      <Box
        className="sidebar-content"
        sx={{
          flex: 1,
          overflowY: "auto",
          overflowX: "hidden",
          p: 2,
          // Custom scrollbar styling
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.divider,
            borderRadius: '3px',
            '&:hover': {
              backgroundColor: theme.palette.text.secondary,
            },
          },
        }}
      >
        <List sx={{ p: 1 }}>
          {menuItems.map((item) => (
            <ListItem key={item.id} disablePadding sx={{ mb: 1.5 }}>
              <ListItemButton
                onClick={() => handleItemClick(item)}
                disabled={item.disabled}
                sx={{
                  borderRadius: 1,
                  py: 1,
                  px: 1.5,
                  backgroundColor:
                    item.isActive && item.isActive(currentFolderId, activeItemId)
                      ? theme.palette.primary.main + "20"
                      : "transparent",
                  "&:hover": {
                    backgroundColor:
                      item.isActive && item.isActive(currentFolderId, activeItemId)
                        ? theme.palette.primary.main + "20"
                        : theme.palette.action.hover,
                  },
                  "&.Mui-disabled": {
                    opacity: 0.5,
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 36,
                    color:
                      item.isActive && item.isActive(currentFolderId, activeItemId)
                        ? theme.palette.primary.main
                        : theme.palette.text.secondary,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  slotProps={{
                    primary: {
                      sx: {
                        fontSize: "0.875rem",
                        fontWeight: item.isActive && item.isActive(currentFolderId, activeItemId) ? 600 : 400,
                        color:
                          item.isActive && item.isActive(currentFolderId, activeItemId)
                            ? theme.palette.primary.main
                            : theme.palette.text.primary,
                      }
                    }
                  }}
                />
                {item.disabled && (
                  <Chip
                    label={t('sidebar.soon')}
                    size="small"
                    sx={{
                      height: 20,
                      fontSize: "0.6rem",
                      backgroundColor: theme.palette.action.hover,
                      color: theme.palette.text.secondary,
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 2, borderColor: "#E5E7EB" }} />

        <Typography
          variant="body2"
          sx={{
            color: "#637381",
            fontWeight: 600,
            mb: 1.5,
            px: 1.5,
            fontSize: "0.75rem",
            textTransform: "uppercase",
            letterSpacing: "0.5px",
          }}
        >
          {t('sidebar.quickAccess')}
        </Typography>

        <List sx={{ p: 1 }}>
          {quickAccessItems.map((item) => (
            <ListItem key={item.id} disablePadding sx={{ mb: 1 }}>
              <ListItemButton
                disabled={item.disabled}
                sx={{
                  borderRadius: 1,
                  py: 1,
                  px: 1.5,
                  "&:hover": {
                    backgroundColor: "#F3F4F6",
                  },
                  "&.Mui-disabled": {
                    opacity: 0.5,
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 36,
                    color: "#637381",
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  slotProps={{
                    primary: {
                      sx: {
                        fontSize: "0.875rem",
                        color: "#637381",
                      }
                    }
                  }}
                />
                {item.disabled && (
                  <Chip
                    label={t('sidebar.soon')}
                    size="small"
                    sx={{
                      height: 20,
                      fontSize: "0.6rem",
                      backgroundColor: "#E5E7EB",
                      color: "#637381",
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Storage indicator - Fixed at bottom */}
      <Box sx={{
        flexShrink: 0,
        p: 2,
        borderTop: `1px solid ${theme.palette.divider}`,
        backgroundColor: theme.palette.background.paper,
      }}>
        <StorageIndicator />
      </Box>
    </Box>
  );
};

export default Sidebar;
