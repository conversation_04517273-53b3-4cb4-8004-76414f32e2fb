.email-verification-banner {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  margin: 64px 0 0 0; /* Add top margin to account for fixed header */
  padding: 0;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
  animation: slideDown 0.3s ease-out;
  position: relative;
  z-index: 10;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.banner-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.banner-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.banner-text {
  flex: 1;
}

.banner-text strong {
  color: #92400e;
  font-size: 1.1rem;
  display: block;
  margin-bottom: 4px;
}

.banner-text p {
  color: #a16207;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.banner-message {
  margin-top: 8px !important;
  font-weight: 500;
}

.banner-message.success {
  color: #059669;
}

.banner-message.error {
  color: #dc2626;
}

.resend-button {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.resend-button:hover:not(:disabled) {
  background: #d97706;
  transform: translateY(-1px);
}

.resend-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
