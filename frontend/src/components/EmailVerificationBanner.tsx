import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import './EmailVerificationBanner.css';

interface EmailVerificationBannerProps {
  user: any;
  onResendVerification: () => void;
}

const EmailVerificationBanner: React.FC<EmailVerificationBannerProps> = ({
  user,
  onResendVerification
}) => {
  const { t } = useTranslation();
  const [isResending, setIsResending] = useState(false);
  const [message, setMessage] = useState('');

  // Only show for local users who haven't verified email
  if (!user || user.provider !== 'local' || user.isEmailVerified) {
    return null;
  }

  const handleResend = async () => {
    setIsResending(true);
    setMessage('');

    try {
      const response = await fetch('/api/v1.0/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: user.email }),
      });

      const data = await response.json();

      if (data.code === 200) {
        setMessage(t('emailVerification.success'));
      } else {
        setMessage(data.message || t('emailVerification.error'));
      }
    } catch (error) {
      setMessage(t('emailVerification.networkError'));
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="email-verification-banner">
      <div className="banner-content">
        <div className="banner-icon">📧</div>
        <div className="banner-text">
          <strong>{t('emailVerification.title')}</strong>
          <p>{t('emailVerification.message')}</p>
          {message && (
            <p className={`banner-message ${message.includes(t('emailVerification.success')) ? 'success' : 'error'}`}>
              {message}
            </p>
          )}
        </div>
        <button
          className="resend-button"
          onClick={handleResend}
          disabled={isResending}
        >
          {isResending ? t('emailVerification.sending') : t('emailVerification.resendButton')}
        </button>
      </div>
    </div>
  );
};

export default EmailVerificationBanner;
