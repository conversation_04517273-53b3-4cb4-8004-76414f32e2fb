/**
 * Utility function to extract a string error message from various error formats
 * Handles backend error responses that may have {head, body} structure
 */
export const getErrorMessage = (error: any, fallback: string = 'An error occurred'): string => {
  // If it's already a string, return it
  if (typeof error === 'string') {
    return error;
  }

  // If it's an Error instance, return the message
  if (error instanceof Error) {
    return error.message;
  }

  // If it has a message property that's a string
  if (error && typeof error.message === 'string') {
    return error.message;
  }

  // If it has a message property that's an object with body/head (backend format)
  if (error && error.message && typeof error.message === 'object') {
    if (error.message.body && typeof error.message.body === 'string') {
      return error.message.body;
    }
    if (error.message.head && typeof error.message.head === 'string') {
      return error.message.head;
    }
  }

  // If it's an object with body/head directly
  if (error && typeof error === 'object') {
    if (error.body && typeof error.body === 'string') {
      return error.body;
    }
    if (error.head && typeof error.head === 'string') {
      return error.head;
    }
  }

  // Return fallback message
  return fallback;
};
