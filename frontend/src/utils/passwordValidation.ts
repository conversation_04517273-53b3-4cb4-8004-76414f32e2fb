export interface PasswordStrength {
  score: number; // 0-4 (0: very weak, 1: weak, 2: fair, 3: good, 4: strong)
  label: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  color: string;
  feedback: string[];
}

export const validatePassword = (password: string): PasswordStrength => {
  let score = 0;
  const feedback: string[] = [];

  // Length check
  if (password.length < 6) {
    feedback.push('Password must be at least 6 characters long');
    return {
      score: 0,
      label: 'very-weak',
      color: '#f44336',
      feedback
    };
  }

  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;

  // Character variety checks
  const hasLowerCase = /[a-z]/.test(password);
  const hasUpperCase = /[A-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

  if (hasLowerCase) score += 1;
  if (hasUpperCase) score += 1;
  if (hasNumbers) score += 1;
  if (hasSpecialChars) score += 1;

  // Provide feedback
  if (!hasLowerCase) feedback.push('Add lowercase letters');
  if (!hasUpperCase) feedback.push('Add uppercase letters');
  if (!hasNumbers) feedback.push('Add numbers');
  if (!hasSpecialChars) feedback.push('Add special characters');
  if (password.length < 8) feedback.push('Use at least 8 characters');

  // Common patterns check
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i
  ];

  const hasCommonPattern = commonPatterns.some(pattern => pattern.test(password));
  if (hasCommonPattern) {
    score = Math.max(0, score - 2);
    feedback.push('Avoid common patterns');
  }

  // Repeated characters check
  const hasRepeatedChars = /(.)\1{2,}/.test(password);
  if (hasRepeatedChars) {
    score = Math.max(0, score - 1);
    feedback.push('Avoid repeated characters');
  }

  // Determine final score and label
  score = Math.min(4, Math.max(0, score));

  let label: PasswordStrength['label'];
  let color: string;

  switch (score) {
    case 0:
    case 1:
      label = 'very-weak';
      color = '#f44336';
      break;
    case 2:
      label = 'weak';
      color = '#ff9800';
      break;
    case 3:
      label = 'fair';
      color = '#ffeb3b';
      break;
    case 4:
      label = 'good';
      color = '#8bc34a';
      break;
    case 5:
    case 6:
      label = 'strong';
      color = '#4caf50';
      break;
    default:
      label = 'weak';
      color = '#ff9800';
  }

  return {
    score,
    label,
    color,
    feedback: feedback.slice(0, 3) // Limit to 3 most important feedback items
  };
};

export const getPasswordStrengthText = (label: PasswordStrength['label']): string => {
  switch (label) {
    case 'very-weak':
      return 'Very Weak';
    case 'weak':
      return 'Weak';
    case 'fair':
      return 'Fair';
    case 'good':
      return 'Good';
    case 'strong':
      return 'Strong';
    default:
      return 'Unknown';
  }
};
