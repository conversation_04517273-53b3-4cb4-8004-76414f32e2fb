import axios from 'axios';
import { ApiResponse, FolderContent } from '../types';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3001',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.token = token;
  }
  return config;
});

export const fileApi = {
  browseFolder: async (folderId?: string): Promise<ApiResponse> => {
    const response = await api.get(`/folders/${folderId || 'root'}`);
    return response.data;
  },

  uploadFile: async (
    file: File,
    folderId?: string,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    if (folderId) {
      formData.append('folderId', folderId);
    }

    const response = await api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  },

  createFolder: async (name: string, parentId?: string): Promise<ApiResponse> => {
    const response = await api.post('api/v1.0/folders/create', {
      name,
      parentId,
    });
    return response.data;
  },

  renameItem: async (itemId: string, newName: string): Promise<ApiResponse> => {
    const response = await api.put(`/items/${itemId}/rename`, {
      name: newName,
    });
    return response.data;
  },

  deleteItem: async (itemId: string): Promise<ApiResponse> => {
    const response = await api.delete(`/items/${itemId}`);
    return response.data;
  },

  searchItems: async (query: string): Promise<ApiResponse> => {
    const response = await api.get('/search', {
      params: { q: query },
    });
    return response.data;
  },

  previewFile: (fileId: string): string => {
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    const token = localStorage.getItem('token');
    return `${baseURL}/api/v1.0/files/preview/${fileId}${token ? `?token=${token}` : ''}`;
  },

  downloadFile: (fileId: string): string => {
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    const token = localStorage.getItem('token');
    return `${baseURL}/api/v1.0/files/download/${fileId}${token ? `?token=${token}` : ''}`;
  },

  getFileInfo: async (fileId: string): Promise<ApiResponse> => {
    const response = await api.get(`/api/v1.0/files/info/${fileId}`);
    return response.data;
  },
};