import axios from 'axios';
import { ApiR<PERSON>po<PERSON>, FolderContent, SearchResult } from '../types';

// Use relative URL for API calls to leverage proxy

const api = axios.create({
  baseURL: '/api/v1.0',
  timeout: 30000,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.token = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Backend returns {code, data, message} structure
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('token');
      window.location.href = '/login';
    }

    // Handle error message format
    let errorMessage = 'An error occurred';
    const errorData = error.response?.data;

    if (errorData) {
      if (typeof errorData.message === 'string') {
        errorMessage = errorData.message;
      } else if (errorData.message && typeof errorData.message === 'object') {
        // Handle backend message format {head, body}
        if (errorData.message.body) {
          errorMessage = errorData.message.body;
        } else if (errorData.message.head) {
          errorMessage = errorData.message.head;
        }
      } else if (typeof errorData === 'string') {
        errorMessage = errorData;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    return Promise.reject(new Error(errorMessage));
  }
);

const fileApi = {
  // Upload file
  uploadFile: (file: File, parentId?: string, onProgress?: (progress: number) => void): Promise<ApiResponse> => {
    const formData = new FormData();
    formData.append('file', file);
    if (parentId) {
      formData.append('parentId', parentId);
    }

    return api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  },

  // Download file - call directly to backend API
  downloadFile: (fileId: string): string => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    return `${baseURL}/api/v1.0/files/download/${fileId}${token ? `?token=${token}` : ''}`;
  },

  // Preview file - call directly to backend API
  previewFile: (fileId: string): string => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001';
    return `${baseURL}/api/v1.0/files/preview/${fileId}${token ? `?token=${token}` : ''}`;
  },

  // Browse folder content
  browseFolder: (folderId?: string): Promise<ApiResponse<FolderContent>> => {
    const url = folderId ? `/browse/${folderId}` : '/browse';
    return api.get(url);
  },

  // Create folder
  createFolder: (folderName: string, parentId?: string): Promise<ApiResponse> => {
    return api.post('/folders/create', {
      folderName,
      parentId: parentId || null,
    });
  },

  // Rename item
  renameItem: (itemId: string, newName: string): Promise<ApiResponse> => {
    return api.put(`/items/${itemId}/rename`, {
      name: newName,
    });
  },

  // Delete item
  deleteItem: (itemId: string): Promise<ApiResponse> => {
    return api.delete(`/items/${itemId}`);
  },

  // Search
  search: (query: string): Promise<ApiResponse<SearchResult>> => {
    return api.get('/search', {
      params: { q: query },
    });
  },

  // Get file info
  getFileInfo: (fileId: string): Promise<ApiResponse> => {
    return api.get(`/files/info/${fileId}`);
  },

  // Storage management
  getStorageInfo: (): Promise<ApiResponse> => {
    return api.get('/user/storage');
  },

  syncStorageStats: (): Promise<ApiResponse> => {
    return api.post('/user/storage-sync');
  },
};

export { fileApi };
export default api;
