/* Mobile Responsive Styles for TeleStore */

/* Base responsive breakpoints */
@media (max-width: 768px) {
  /* Global mobile adjustments */
  body {
    font-size: 14px;
  }

  /* Header adjustments */
  .MuiAppBar-root {
    padding: 0 8px;
  }

  .MuiToolbar-root {
    min-height: 56px !important;
    padding: 0 8px;
  }

  /* Sidebar adjustments */
  .MuiDrawer-paper {
    width: 260px !important;
    overflow: hidden !important;
  }

  /* Sidebar scrollable content */
  .sidebar-content {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch;
  }

  /* Custom scrollbar for mobile */
  .sidebar-content::-webkit-scrollbar {
    width: 4px !important;
  }

  .sidebar-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-radius: 2px !important;
  }

  /* Content area adjustments */
  .content-area {
    padding: 16px 8px !important;
  }

  /* Action bar mobile layout */
  .action-bar {
    flex-direction: column;
    gap: 8px;
  }

  .action-bar .MuiButton-root {
    width: 100%;
    justify-content: center;
  }

  /* File grid mobile layout */
  .file-grid-header {
    grid-template-columns: 1fr 40px !important;
  }

  .file-grid-item {
    grid-template-columns: 1fr 40px !important;
    padding: 12px 8px !important;
  }

  /* Breadcrumbs mobile */
  .MuiBreadcrumbs-root {
    font-size: 14px;
  }

  .MuiBreadcrumbs-li {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Dialog mobile adjustments */
  .MuiDialog-paper {
    margin: 16px !important;
    width: calc(100% - 32px) !important;
    max-width: none !important;
  }

  /* Form elements mobile */
  .MuiTextField-root {
    margin-bottom: 16px;
  }

  /* Button adjustments */
  .MuiButton-root {
    min-width: 44px;
    padding: 8px 16px;
  }

  .MuiIconButton-root {
    padding: 8px;
  }

  /* Typography mobile scaling */
  .MuiTypography-h4 {
    font-size: 1.5rem !important;
  }

  .MuiTypography-h5 {
    font-size: 1.25rem !important;
  }

  .MuiTypography-h6 {
    font-size: 1.1rem !important;
  }

  /* Menu mobile adjustments */
  .MuiMenu-paper {
    max-width: calc(100vw - 32px) !important;
  }

  /* Search field mobile */
  .search-field {
    max-width: none !important;
  }

  /* File preview mobile */
  .file-preview-dialog .MuiDialog-paper {
    height: 90vh !important;
    max-height: 90vh !important;
  }

  /* Upload dialog mobile */
  .upload-dialog .MuiDialog-paper {
    height: auto !important;
    max-height: 80vh !important;
  }

  /* Settings page mobile */
  .settings-container {
    padding: 16px 8px !important;
  }

  /* Storage indicator mobile */
  .storage-indicator {
    flex-direction: column;
    text-align: center;
  }

  /* Login page mobile */
  .login-container {
    padding: 16px !important;
  }

  .login-form {
    width: 100% !important;
    max-width: none !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  /* Even smaller adjustments */
  .MuiToolbar-root {
    min-height: 48px !important;
  }

  .MuiButton-root {
    font-size: 0.75rem;
    padding: 6px 12px;
  }

  .MuiTypography-h6 {
    font-size: 1rem !important;
  }

  /* Compact file grid */
  .file-grid-item {
    padding: 8px 4px !important;
  }

  /* Smaller dialogs */
  .MuiDialog-paper {
    margin: 8px !important;
    width: calc(100% - 16px) !important;
  }

  /* Compact action bar */
  .action-bar .MuiButton-root {
    font-size: 0.75rem;
    padding: 6px 12px;
  }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  /* Tablet specific styles */
  .MuiDrawer-paper {
    width: 260px !important;
    overflow: hidden !important;
  }

  .content-area {
    padding: 24px 16px !important;
  }

  /* File grid tablet layout */
  .file-grid-header {
    grid-template-columns: 1fr 100px 100px 40px !important;
  }

  .file-grid-item {
    grid-template-columns: 1fr 100px 100px 40px !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Larger touch targets */
  .MuiIconButton-root {
    min-width: 44px;
    min-height: 44px;
  }

  .MuiButton-root {
    min-height: 44px;
  }

  /* Better touch feedback */
  .MuiListItemButton-root {
    min-height: 48px;
  }

  /* File items touch optimization */
  .file-grid-item {
    min-height: 56px;
  }
}

/* Dark mode mobile adjustments */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  /* Dark mode specific mobile styles */
  .MuiAppBar-root {
    background-color: #1e293b !important;
  }

  .MuiDrawer-paper {
    background-color: #1e293b !important;
  }

  /* Dark mode scrollbar */
  .sidebar-content::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  /* Landscape specific adjustments */
  .MuiToolbar-root {
    min-height: 48px !important;
  }

  .content-area {
    padding: 8px !important;
  }

  /* Compact layout for landscape */
  .action-bar {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .action-bar .MuiButton-root {
    width: auto;
    flex: 1;
    min-width: 120px;
  }
}

/* High DPI mobile displays */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  /* Crisp icons and text on retina displays */
  .MuiSvgIcon-root {
    -webkit-font-smoothing: antialiased;
  }

  .MuiTypography-root {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
  /* Better focus indicators */
  .MuiButton-root:focus,
  .MuiIconButton-root:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
  }

  /* Larger text for better readability */
  .MuiTypography-body2 {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
  }

  /* Better spacing for touch */
  .MuiListItem-root {
    padding: 12px 16px !important;
  }
}
