export interface FileItem {
  id: string;
  name: string;
  type: 'file';
  size: number;
  mimeType: string;
  uploadDate: string;
  parentId: string | null;
  telegramFileId: string;
}

export interface FolderItem {
  id: string;
  name: string;
  type: 'folder';
  createdAt: string;
  parentId: string | null;
}

export type Item = FileItem | FolderItem;

export interface FolderContent {
  folders: FolderItem[];
  files: FileItem[];
}

export interface SearchResult {
  query: string;
  totalResults: number;
  folders: FolderItem[];
  files: FileItem[];
}

export interface ApiResponse<T = any> {
  code: number;
  data?: T;
  message: string;
}

export interface BreadcrumbItem {
  id: string | null;
  name: string;
}

export interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}
