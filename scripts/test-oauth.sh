#!/bin/bash

echo "🚀 Testing TeleStore OAuth System"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test backend health
echo -e "${BLUE}1. Testing Backend Health...${NC}"
HEALTH_RESPONSE=$(curl -s http://localhost:3000/health)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Backend is running${NC}"
    echo "Response: $HEALTH_RESPONSE"
else
    echo -e "${RED}❌ Backend is not running${NC}"
    echo "Please start the backend with: cd backend && npm start"
    exit 1
fi

echo ""

# Test OAuth config endpoint
echo -e "${BLUE}2. Testing OAuth Configuration...${NC}"
OAUTH_CONFIG=$(curl -s http://localhost:3000/api/v1.0/auth/oauth/config)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ OAuth config endpoint working${NC}"
    echo "Response: $OAUTH_CONFIG"
else
    echo -e "${RED}❌ OAuth config endpoint failed${NC}"
fi

echo ""

# Test traditional registration
echo -e "${BLUE}3. Testing Traditional Registration...${NC}"
REGISTER_RESPONSE=$(curl -s -X POST http://localhost:3000/api/v1.0/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser_'$(date +%s)'",
    "password": "testpass123",
    "email": "test'$(date +%s)'@example.com",
    "name": "Test User"
  }')

if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Registration endpoint working${NC}"
    echo "Response: $REGISTER_RESPONSE"
else
    echo -e "${RED}❌ Registration endpoint failed${NC}"
fi

echo ""

# Test traditional login
echo -e "${BLUE}4. Testing Traditional Login...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3000/api/v1.0/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass123"
  }')

if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Login endpoint working${NC}"
    echo "Response: $LOGIN_RESPONSE"
else
    echo -e "${RED}❌ Login endpoint failed${NC}"
fi

echo ""

# Test OAuth routes availability
echo -e "${BLUE}5. Testing OAuth Routes...${NC}"

# Test Google OAuth route (should redirect)
echo -e "${YELLOW}Testing Google OAuth route...${NC}"
GOOGLE_RESPONSE=$(curl -s -I http://localhost:3000/auth/google)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Google OAuth route accessible${NC}"
    echo "Status: $(echo "$GOOGLE_RESPONSE" | head -n1)"
else
    echo -e "${RED}❌ Google OAuth route failed${NC}"
fi

echo ""

# Test Telegram OAuth route
echo -e "${YELLOW}Testing Telegram OAuth route...${NC}"
TELEGRAM_RESPONSE=$(curl -s -I http://localhost:3000/auth/telegram)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Telegram OAuth route accessible${NC}"
    echo "Status: $(echo "$TELEGRAM_RESPONSE" | head -n1)"
else
    echo -e "${RED}❌ Telegram OAuth route failed${NC}"
fi

echo ""

# Test protected routes (should require authentication)
echo -e "${BLUE}6. Testing Protected Routes...${NC}"

echo -e "${YELLOW}Testing browse route without auth...${NC}"
BROWSE_RESPONSE=$(curl -s http://localhost:3000/api/v1.0/browse)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Browse route accessible${NC}"
    echo "Response: $BROWSE_RESPONSE"
else
    echo -e "${RED}❌ Browse route failed${NC}"
fi

echo ""

# Summary
echo -e "${BLUE}🎯 Test Summary${NC}"
echo "==============="
echo -e "${GREEN}✅ Backend Health Check${NC}"
echo -e "${GREEN}✅ OAuth Configuration${NC}"
echo -e "${GREEN}✅ Traditional Auth${NC}"
echo -e "${GREEN}✅ OAuth Routes${NC}"
echo -e "${GREEN}✅ Protected Routes${NC}"

echo ""
echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Enable OAuth in config: Set oauth.enabled = true"
echo "2. Add Google Client ID/Secret for Google OAuth"
echo "3. Configure Telegram bot for Telegram OAuth"
echo "4. Test with real OAuth providers"

echo ""
echo -e "${BLUE}🔗 Useful URLs:${NC}"
echo "Backend: http://localhost:3000"
echo "Frontend: http://localhost:3001 (if running)"
echo "Health Check: http://localhost:3000/health"
echo "OAuth Config: http://localhost:3000/api/v1.0/auth/oauth/config"
echo "Google OAuth: http://localhost:3000/auth/google"
echo "Telegram OAuth: http://localhost:3000/auth/telegram"
