#!/bin/bash

# TeleStore Setup Script
echo "🚀 Welcome to TeleStore Setup!"
echo "This script will help you configure and start your Telegram-based file storage system."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to prompt for input
prompt_input() {
    local prompt_text=$1
    local var_name=$2
    local default_value=$3
    
    if [ -n "$default_value" ]; then
        echo -n -e "${BLUE}$prompt_text [$default_value]: ${NC}"
    else
        echo -n -e "${BLUE}$prompt_text: ${NC}"
    fi
    
    read input
    if [ -z "$input" ] && [ -n "$default_value" ]; then
        input=$default_value
    fi
    
    eval "$var_name='$input'"
}

# Check if .env file exists
if [ -f ".env" ]; then
    echo -e "${YELLOW}⚠ .env file already exists. Do you want to reconfigure? (y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "Using existing configuration..."
        echo ""
        echo "🐳 Starting Docker Compose..."
        docker-compose up -d
        echo ""
        echo "✅ TeleStore is starting up!"
        echo "📱 Access the application at: http://localhost"
        echo "🔧 Run './scripts/test-system.sh' to verify the installation"
        exit 0
    fi
fi

echo "📋 Let's configure your TeleStore installation..."
echo ""

# Telegram Bot Configuration
echo -e "${GREEN}🤖 Telegram Bot Configuration${NC}"
echo "You need to create a Telegram bot and get the bot token and chat ID."
echo "1. Message @BotFather on Telegram"
echo "2. Send /newbot and follow the instructions"
echo "3. Copy the bot token"
echo "4. Create a chat/channel and add your bot"
echo "5. Get the chat ID (you can use @userinfobot)"
echo ""

prompt_input "Enter your Telegram Bot Token" TELEGRAM_BOT_TOKEN
prompt_input "Enter your Telegram Chat ID" TELEGRAM_CHAT_ID

# Database Configuration
echo ""
echo -e "${GREEN}🗄️ Database Configuration${NC}"
prompt_input "MongoDB URI" MONGODB_URI "mongodb://localhost:27017/tele-store"
prompt_input "Redis Host" REDIS_HOST "localhost"
prompt_input "Redis Port" REDIS_PORT "6379"

# Application Configuration
echo ""
echo -e "${GREEN}⚙️ Application Configuration${NC}"
prompt_input "Environment (development/production)" NODE_ENV "development"
prompt_input "Backend Port" PORT "3000"

# Generate random secret key
SECRET_KEY=$(openssl rand -base64 32 2>/dev/null || date +%s | sha256sum | base64 | head -c 32)
echo -e "${BLUE}JWT Secret Key: ${NC}[Generated automatically]"

# Frontend Configuration
echo ""
echo -e "${GREEN}🌐 Frontend Configuration${NC}"
if [ "$NODE_ENV" = "production" ]; then
    prompt_input "API URL" REACT_APP_API_URL "http://localhost:3000"
else
    REACT_APP_API_URL="http://localhost:3000"
    echo -e "${BLUE}API URL: ${NC}$REACT_APP_API_URL (development default)"
fi

# Create .env file
echo ""
echo "📝 Creating .env file..."

cat > .env << EOF
# Telegram Configuration
TELEGRAM_BOT_TOKEN=$TELEGRAM_BOT_TOKEN
TELEGRAM_CHAT_ID=$TELEGRAM_CHAT_ID

# Database Configuration
MONGODB_URI=$MONGODB_URI
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT

# Application Configuration
NODE_ENV=$NODE_ENV
PORT=$PORT
SECRET_KEY=$SECRET_KEY

# Frontend Configuration
REACT_APP_API_URL=$REACT_APP_API_URL

# Email Configuration (Optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EOF

echo -e "${GREEN}✅ Configuration saved to .env file${NC}"

# Update backend config
echo ""
echo "📝 Updating backend configuration..."
cp backend/config-template.json backend/config/default.json

# Replace placeholders in config
sed -i.bak "s/your_telegram_bot_token/$TELEGRAM_BOT_TOKEN/g" backend/config/default.json
sed -i.bak "s/your_telegram_chat_id/$TELEGRAM_CHAT_ID/g" backend/config/default.json
sed -i.bak "s/your_secret_key_for_jwt/$SECRET_KEY/g" backend/config/default.json
rm backend/config/default.json.bak 2>/dev/null || true

echo -e "${GREEN}✅ Backend configuration updated${NC}"

# Check Docker
echo ""
echo "🐳 Checking Docker..."
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker is available${NC}"

# Start the application
echo ""
echo "🚀 Starting TeleStore..."
echo "This may take a few minutes for the first time..."

docker-compose up -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 15

# Run system test
echo ""
echo "🧪 Running system test..."
if [ -f "./scripts/test-system.sh" ]; then
    ./scripts/test-system.sh
else
    echo -e "${YELLOW}⚠ Test script not found, skipping tests${NC}"
fi

echo ""
echo -e "${GREEN}🎉 TeleStore setup completed!${NC}"
echo ""
echo "📱 Access your TeleStore at: http://localhost"
echo "🔧 Backend API: http://localhost:3000"
echo "📊 Health check: http://localhost:3000/health"
echo ""
echo "📚 Useful commands:"
echo "  - View logs: docker-compose logs -f"
echo "  - Stop services: docker-compose down"
echo "  - Restart services: docker-compose restart"
echo "  - Run tests: ./scripts/test-system.sh"
echo ""
echo "🆘 Need help? Check the README.md file or create an issue on GitHub."
